<template>
  <div class="map-container">
    <LMap
      ref="map"
      :zoom="zoom"
      :center="center"
      :options="mapOptions"
      class="map"
      @ready="onMapReady"
    >
      <LTileLayer
        :url="tileLayerUrl"
        :attribution="attribution"
        :options="tileLayerOptions"
      />
      
      <!-- Default marker at center -->
      <LMarker
        v-if="showCenterMarker"
        :lat-lng="center"
        :options="centerMarkerOptions"
      >
        <LPopup v-if="centerMarkerPopup">
          {{ centerMarkerPopup }}
        </LPopup>
      </LMarker>
      
      <!-- Custom markers -->
      <LMarker
        v-for="marker in markers"
        :key="marker.id"
        :lat-lng="[marker.lat, marker.lng]"
        :options="marker.options || {}"
      >
        <LPopup v-if="marker.popup">
          <div v-html="marker.popup"></div>
        </LPopup>
      </LMarker>
      
      <!-- Circles -->
      <LCircle
        v-for="circle in circles"
        :key="circle.id"
        :lat-lng="[circle.lat, circle.lng]"
        :radius="circle.radius"
        :options="circle.options || {}"
      >
        <LPopup v-if="circle.popup">
          <div v-html="circle.popup"></div>
        </LPopup>
      </LCircle>
      
      <!-- Polygons -->
      <LPolygon
        v-for="polygon in polygons"
        :key="polygon.id"
        :lat-lngs="polygon.points"
        :options="polygon.options || {}"
      >
        <LPopup v-if="polygon.popup">
          <div v-html="polygon.popup"></div>
        </LPopup>
      </LPolygon>
    </LMap>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import {
  LMap,
  LTileLayer,
  LMarker,
  LPopup,
  LCircle,
  LPolygon
} from '@vue-leaflet/vue-leaflet'
import 'leaflet/dist/leaflet.css'

// Fix for default markers in Leaflet with Vite
import L from 'leaflet'
import markerIcon from 'leaflet/dist/images/marker-icon.png'
import markerIcon2x from 'leaflet/dist/images/marker-icon-2x.png'
import markerShadow from 'leaflet/dist/images/marker-shadow.png'

// Fix default icon paths
delete L.Icon.Default.prototype._getIconUrl
L.Icon.Default.mergeOptions({
  iconUrl: markerIcon,
  iconRetinaUrl: markerIcon2x,
  shadowUrl: markerShadow,
})

// Props
const props = defineProps({
  // Map center coordinates [lat, lng]
  center: {
    type: Array,
    default: () => [-6.2088, 106.8456] // Jakarta, Indonesia
  },
  // Initial zoom level
  zoom: {
    type: Number,
    default: 13
  },
  // Map height
  height: {
    type: String,
    default: '400px'
  },
  // Show marker at center
  showCenterMarker: {
    type: Boolean,
    default: true
  },
  // Center marker popup text
  centerMarkerPopup: {
    type: String,
    default: 'Map Center'
  },
  // Custom markers array
  markers: {
    type: Array,
    default: () => []
  },
  // Circles array
  circles: {
    type: Array,
    default: () => []
  },
  // Polygons array
  polygons: {
    type: Array,
    default: () => []
  },
  // Tile layer URL
  tileLayerUrl: {
    type: String,
    default: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
  },
  // Attribution
  attribution: {
    type: String,
    default: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
  }
})

// Emits
const emit = defineEmits(['map-ready', 'map-click', 'marker-click'])

// Refs
const map = ref(null)

// Map options
const mapOptions = {
  zoomControl: true,
  attributionControl: true,
  scrollWheelZoom: true,
  doubleClickZoom: true,
  boxZoom: true,
  keyboard: true,
  dragging: true,
  touchZoom: true
}

// Tile layer options
const tileLayerOptions = {
  maxZoom: 19,
  subdomains: ['a', 'b', 'c']
}

// Center marker options
const centerMarkerOptions = {
  draggable: false,
  riseOnHover: true
}

// Methods
const onMapReady = () => {
  emit('map-ready', map.value?.leafletObject)
}

// Watch for center changes
watch(() => props.center, (newCenter) => {
  if (map.value?.leafletObject) {
    map.value.leafletObject.setView(newCenter, props.zoom)
  }
})

// Watch for zoom changes
watch(() => props.zoom, (newZoom) => {
  if (map.value?.leafletObject) {
    map.value.leafletObject.setZoom(newZoom)
  }
})

onMounted(() => {
  // Additional setup if needed
})
</script>

<style scoped>
.map-container {
  width: 100%;
  height: v-bind(height);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.map {
  width: 100%;
  height: 100%;
}

/* Custom popup styles */
:deep(.leaflet-popup-content-wrapper) {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

:deep(.leaflet-popup-tip) {
  background: white;
}

/* Custom control styles */
:deep(.leaflet-control-zoom) {
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.leaflet-control-zoom a) {
  border-radius: 6px;
  transition: all 0.2s ease;
}

:deep(.leaflet-control-zoom a:hover) {
  background-color: #f3f4f6;
}
</style>
